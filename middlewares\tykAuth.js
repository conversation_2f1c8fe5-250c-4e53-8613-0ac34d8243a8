const jwt = require('jsonwebtoken');
const axios = require('axios');
const { status: httpStatus } = require("http-status");
const { ApiError } = require("../helpers/api.helper");
const tykConfig = require('../config/tyk');
const logger = require('../config/logger');
const { Identity } = require('../models');

/**
 * Tyk Authentication Middleware
 * Handles JWT validation and custom authentication through Tyk Gateway
 */

/**
 * Verify JWT token with Tyk configuration
 */
const verifyTykJWT = async (token) => {
  try {
    // Decode and verify JWT using Tyk's secret
    const decoded = jwt.verify(token, tykConfig.secret);
    
    // Validate token type and required fields
    if (!decoded.sub) {
      throw new Error('Token missing subject (sub) claim');
    }

    return decoded;
  } catch (error) {
    logger.error('Tyk JWT verification failed:', error.message);
    throw new Error('Invalid or expired token');
  }
};

/**
 * Validate token with Tyk Gateway introspection
 */
const introspectTykToken = async (token) => {
  try {
    const response = await axios.post(
      `${tykConfig.gatewayUrl}/tyk/oauth/introspect`,
      { token },
      {
        headers: tykConfig.getGatewayHeaders(),
        timeout: 5000
      }
    );

    if (!response.data.active) {
      throw new Error('Token is not active');
    }

    return response.data;
  } catch (error) {
    logger.error('Tyk token introspection failed:', error.message);
    throw new Error('Token validation failed');
  }
};

/**
 * Get identity from database and enrich with permissions
 */
const getIdentityWithPermissions = async (identityId) => {
  try {
    const identity = await Identity.findByPk(identityId, {
      include: [
        {
          association: 'role',
          include: [
            {
              association: 'permission',
              attributes: ['name']
            }
          ]
        }
      ]
    });

    if (!identity) {
      throw new Error('Identity not found');
    }

    // Extract permissions from roles
    const permissions = [];
    if (identity.role && identity.role.length > 0) {
      identity.role.forEach(role => {
        if (role.permission && role.permission.length > 0) {
          role.permission.forEach(permission => {
            if (!permissions.includes(permission.name)) {
              permissions.push(permission.name);
            }
          });
        }
      });
    }

    // Add permissions to identity object
    identity.permissions = permissions;
    
    return identity;
  } catch (error) {
    logger.error('Failed to get identity with permissions:', error.message);
    throw error;
  }
};

/**
 * Tyk authentication callback
 */
const tykVerifyCallback = (req, resolve, reject, requiredRights) => async (tokenData) => {
  try {
    let identity;
    let identityId;

    // Extract identity ID from token data
    if (tokenData.sub) {
      identityId = tokenData.sub;
    } else if (tokenData.user_id) {
      identityId = tokenData.user_id;
    } else {
      throw new Error('No identity identifier found in token');
    }

    // Get identity with permissions from database
    identity = await getIdentityWithPermissions(identityId);

    // Set identity in request
    req.identity = identity;

    // Check required permissions
    if (requiredRights.length > 0) {
      const identityRights = identity.permissions || [];

      if (identityRights.length === 0) {
        return reject(
          new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
        );
      }

      // Check if identity has all required rights
      const hasRequiredRights = requiredRights.every((requiredRight) =>
        identityRights.includes(requiredRight)
      );

      // Allow access if user has required rights OR if accessing their own resource
      if (!hasRequiredRights && req.params.identityId !== identity.identity_id) {
        return reject(
          new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity")
        );
      }
    }

    // Log successful authentication
    logger.info(`Tyk authentication successful for identity: ${identity.identity_id}`);
    resolve();

  } catch (error) {
    logger.error('Tyk authentication failed:', error.message);
    reject(new ApiError(httpStatus.UNAUTHORIZED, error.message));
  }
};

/**
 * Main Tyk authentication middleware
 */
const tykAuth = (...requiredRights) => async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new ApiError(httpStatus.UNAUTHORIZED, "Authorization header missing or invalid");
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    return new Promise(async (resolve, reject) => {
      try {
        let tokenData;

        // Try JWT verification first (faster)
        try {
          tokenData = await verifyTykJWT(token);
          logger.debug('Token verified using JWT validation');
        } catch (jwtError) {
          // Fallback to introspection if JWT verification fails
          logger.debug('JWT verification failed, trying introspection');
          tokenData = await introspectTykToken(token);
          logger.debug('Token verified using introspection');
        }

        // Process authentication with token data
        await tykVerifyCallback(req, resolve, reject, requiredRights)(tokenData);

      } catch (error) {
        reject(new ApiError(httpStatus.UNAUTHORIZED, error.message));
      }
    })
      .then(() => next())
      .catch((err) => next(err));

  } catch (error) {
    next(error);
  }
};

module.exports = tykAuth;
